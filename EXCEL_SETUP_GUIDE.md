# 📊 دليل إعداد دعم ملفات Excel - نظام إدارة المنتجات

## نظرة عامة

نظام إدارة المنتجات يدعم استيراد ملفات Excel بطريقتين:

1. **الطريقة الأساسية** - تعمل حالياً مع ملفات CSV وExcel المحفوظة كـ CSV
2. **الطريقة المتقدمة** - تتطلب تثبيت مكتبة PhpSpreadsheet لدعم كامل لملفات Excel

## 🚀 الحالة الحالية

### ✅ ما يعمل الآن:
- استيراد ملفات CSV بالكامل
- استيراد ملفات Excel المحفوظة بصيغة CSV
- نموذج Excel قابل للتحميل
- التحقق المتقدم من البيانات
- دعم النصوص العربية
- معاينة البيانات قبل الاستيراد

### ⚠️ ما يحتاج تحسين:
- قراءة ملفات Excel الأصلية (.xlsx, .xls) مباشرة
- تنسيق متقدم لملفات Excel المُصدرة

## 🛠️ تثبيت مكتبة PhpSpreadsheet (اختياري)

### الطريقة 1: باستخدام Composer

```bash
# في مجلد المشروع
composer install

# أو إذا لم يكن composer.json محدث
composer require phpoffice/phpspreadsheet
```

### الطريقة 2: التحميل اليدوي

1. تحميل مكتبة PhpSpreadsheet من [GitHub](https://github.com/PHPOffice/PhpSpreadsheet)
2. استخراج الملفات في مجلد `vendor/`
3. التأكد من وجود ملف `vendor/autoload.php`

### الطريقة 3: استخدام XAMPP/WAMP

```bash
# في مجلد htdocs/invoice
composer init
composer require phpoffice/phpspreadsheet
```

## 📋 التحقق من التثبيت

### اختبار سريع:

قم بإنشاء ملف `test_excel.php` في مجلد المشروع:

```php
<?php
require_once 'config/config.php';

echo "<h2>اختبار دعم Excel</h2>";

// اختبار وجود مجلد vendor
if (file_exists('vendor/autoload.php')) {
    echo "<p style='color: green;'>✅ مجلد vendor موجود</p>";
    
    require_once 'vendor/autoload.php';
    
    // اختبار مكتبة PhpSpreadsheet
    if (class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
        echo "<p style='color: green;'>✅ مكتبة PhpSpreadsheet متوفرة</p>";
        echo "<p><strong>النتيجة:</strong> دعم كامل لملفات Excel!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ مكتبة PhpSpreadsheet غير متوفرة</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ مجلد vendor غير موجود</p>";
}

// اختبار مكتبة SimpleExcelReader
require_once 'includes/SimpleExcelReader.php';
echo "<p style='color: green;'>✅ مكتبة SimpleExcelReader متوفرة</p>";
echo "<p><strong>النتيجة:</strong> دعم أساسي لملفات CSV وExcel!</p>";
?>
```

## 🎯 الاستخدام الحالي

### للمستخدمين:

1. **استيراد ملفات CSV**: يعمل بشكل مثالي
2. **استيراد ملفات Excel**: 
   - احفظ ملف Excel كـ CSV (UTF-8)
   - أو استخدم النموذج المتوفر

### للمطورين:

```php
// استخدام SimpleExcelReader
require_once 'includes/SimpleExcelReader.php';

try {
    $reader = new SimpleExcelReader($file_path);
    $reader->validateFile();
    $data = $reader->readFile();
    
    // معالجة البيانات
    foreach ($data as $row) {
        // ...
    }
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
```

## 📊 مقارنة الطرق

| الميزة | SimpleExcelReader | PhpSpreadsheet |
|--------|------------------|----------------|
| ملفات CSV | ✅ كامل | ✅ كامل |
| ملفات Excel | ⚠️ محدود | ✅ كامل |
| سهولة التثبيت | ✅ مدمج | ⚠️ يحتاج composer |
| حجم المكتبة | ✅ صغير | ⚠️ كبير |
| الأداء | ✅ سريع | ⚠️ أبطأ |
| المميزات المتقدمة | ❌ محدود | ✅ شامل |

## 🔧 استكشاف الأخطاء

### مشكلة: "vendor/autoload.php not found"

**الحل:**
```bash
cd /path/to/your/project
composer install
```

### مشكلة: "Class PhpOffice\PhpSpreadsheet\IOFactory not found"

**الحل:**
```bash
composer require phpoffice/phpspreadsheet
```

### مشكلة: ملفات Excel لا تُقرأ

**الحل:**
1. تأكد من تثبيت PhpSpreadsheet
2. أو احفظ الملف كـ CSV
3. استخدم النموذج المتوفر

## 📈 خطة التطوير المستقبلية

### المرحلة 1 (مكتملة):
- ✅ نظام استيراد CSV
- ✅ مكتبة SimpleExcelReader
- ✅ واجهة مستخدم محسنة
- ✅ التحقق من البيانات

### المرحلة 2 (قيد التطوير):
- 🔄 دعم كامل لملفات Excel
- 🔄 تحسين الأداء
- 🔄 مميزات متقدمة

### المرحلة 3 (مخطط لها):
- 📋 تصدير Excel متقدم
- 📋 قوالب Excel مخصصة
- 📋 معالجة الصور في Excel
- 📋 تحليل البيانات

## 🆘 الدعم والمساعدة

### للمستخدمين:
- استخدم النموذج المتوفر
- احفظ ملفات Excel كـ CSV
- راجع دليل الاستيراد

### للمطورين:
- راجع ملف `includes/SimpleExcelReader.php`
- اقرأ تعليقات الكود
- اختبر مع ملفات مختلفة

### الإبلاغ عن المشاكل:
1. وصف المشكلة بالتفصيل
2. نوع الملف المستخدم
3. رسالة الخطأ (إن وجدت)
4. خطوات إعادة إنتاج المشكلة

## 📝 ملاحظات مهمة

1. **الأمان**: جميع الملفات المرفوعة يتم التحقق منها
2. **الأداء**: الحد الأقصى 5MB لكل ملف
3. **التوافق**: يدعم جميع إصدارات PHP 7.4+
4. **الترميز**: دعم كامل للنصوص العربية

---

**تاريخ التحديث**: 2024  
**الإصدار**: 2.0  
**الحالة**: نشط ومدعوم
