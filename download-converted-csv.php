<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

try {
    if (!isset($_GET['file']) || empty($_GET['file'])) {
        throw new Exception('اسم الملف مفقود');
    }
    
    $filename = $_GET['file'];
    
    // التحقق من أمان اسم الملف
    if (!preg_match('/^converted_[a-zA-Z0-9_\-\.]+\.csv$/', $filename)) {
        throw new Exception('اسم الملف غير صحيح');
    }
    
    $file_path = sys_get_temp_dir() . '/' . $filename;
    
    if (!file_exists($file_path)) {
        throw new Exception('الملف غير موجود أو انتهت صلاحيته');
    }
    
    // إعداد headers للتحميل
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // إرسال الملف
    readfile($file_path);
    
    // حذف الملف المؤقت بعد التحميل
    unlink($file_path);
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، إعادة توجيه مع رسالة خطأ
    setMessage('خطأ في تحميل الملف: ' . $e->getMessage(), 'error');
    redirect('convert-excel-to-csv.php');
}
?>
