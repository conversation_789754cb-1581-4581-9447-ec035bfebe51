# دليل قوالب الفواتير

## نظرة عامة
يحتوي النظام على مجموعة متنوعة من قوالب الفواتير المصممة لتلبية احتياجات مختلف أنواع الأعمال.

## القوالب المتاحة

### 1. القالب الكلاسيكي (Classic Template)
- **الوصف**: قالب بسيط ومهني للاستخدام العام
- **الفئة**: classic
- **المميزات**: تصميم نظيف وبسيط، سهل القراءة

### 2. القالب الحديث (Modern Template)
- **الوصف**: قالب عصري بألوان جذابة
- **الفئة**: modern
- **المميزات**: تصميم حديث مع ألوان متدرجة

### 3. القالب المهني (Professional Template)
- **الوصف**: قالب احترافي للشركات
- **الفئة**: professional
- **المميزات**: تصميم رسمي ومناسب للشركات الكبيرة

### 4. القالب الحديث المتطور (Modern Advanced Template) - جديد!
- **الوصف**: قالب حديث ومتطور مع تصميم عصري وألوان جذابة مستوحى من المدن الحديثة
- **الفئة**: modern
- **المميزات**:
  - تصميم مستوحى من أفق المدن الحديثة
  - ألوان متدرجة باللون الأزرق السماوي (#00bcd4)
  - شعار ديناميكي مع رسوم بيانية
  - تخطيط احترافي مع تقسيم واضح للمعلومات
  - تصميم متجاوب يعمل على جميع الأجهزة
  - معلومات دفع مفصلة
  - تذييل أنيق مع معلومات الاتصال

### 5. القالب الهندسي الحديث (Geometric Modern Template) - جديد!
- **الوصف**: قالب هندسي حديث مع تصميم أنيق وألوان زرقاء متدرجة وأشكال هندسية جذابة
- **الفئة**: modern
- **المميزات**:
  - تصميم هندسي مع أشكال متداخلة
  - ألوان متدرجة زرقاء أنيقة (#1e3c72 إلى #4a90e2)
  - خطوط هندسية ديناميكية في الخلفية
  - تخطيط نظيف ومنظم
  - جدول عناصر واضح ومرقم
  - قسم معلومات الدفع منفصل
  - تذييل هندسي متطابق مع التصميم
  - تصميم متجاوب ومحسن للطباعة

### 6. القالب المتدرج البنفسجي (Gradient Purple Template) - جديد!
- **الوصف**: قالب أنيق مع تدرج لوني من الأزرق إلى البنفسجي مع دوائر متداخلة وتصميم عصري
- **الفئة**: modern
- **المميزات**:
  - تدرج لوني رائع من الأزرق الداكن إلى البنفسجي والأحمر
  - دوائر متداخلة شفافة في الشعار
  - هيدر متدرج مع تأثيرات بصرية جذابة
  - جدول عناصر بتصميم احترافي
  - قسم إجمالي مميز بخلفية متدرجة
  - تخطيط متوازن ومنظم
  - ألوان متناسقة ومريحة للعين
  - تصميم متجاوب ومحسن للطباعة

### 7. القالب الأزرق الأنيق (Elegant Blue Template) - جديد!
- **الوصف**: قالب أنيق باللون الأزرق مع شعار دائري وتصميم نظيف ومهني
- **الفئة**: modern
- **المميزات**:
  - تصميم أنيق باللون الأزرق الاحترافي (#2196F3)
  - شعار دائري مميز مع نقطة مركزية
  - أشرطة زرقاء جانبية للتوازن البصري
  - جدول عناصر واضح ومنظم
  - قسم إجمالي مميز بشكل دائري
  - معلومات دفع مفصلة ومنظمة
  - توقيع أنيق مع خط فاصل
  - تصميم نظيف ومهني للغاية

## المتغيرات المدعومة

جميع القوالب تدعم المتغيرات التالية:

### معلومات الفاتورة
- `{{invoice_number}}` - رقم الفاتورة
- `{{issue_date}}` - تاريخ الإصدار
- `{{due_date}}` - تاريخ الاستحقاق

### معلومات الشركة
- `{{company_name}}` - اسم الشركة
- `{{company_tagline}}` - شعار الشركة (للقالب المتطور)
- `{{company_address}}` - عنوان الشركة
- `{{company_city}}` - مدينة الشركة
- `{{company_country}}` - بلد الشركة
- `{{company_phone}}` - هاتف الشركة
- `{{company_email}}` - بريد الشركة الإلكتروني
- `{{company_website}}` - موقع الشركة الإلكتروني

### معلومات العميل
- `{{client_name}}` - اسم العميل
- `{{client_company}}` - شركة العميل
- `{{client_address}}` - عنوان العميل
- `{{client_city}}` - مدينة العميل
- `{{client_country}}` - بلد العميل
- `{{client_phone}}` - هاتف العميل
- `{{client_email}}` - بريد العميل الإلكتروني

### المبالغ المالية
- `{{subtotal}}` - المجموع الفرعي
- `{{tax_amount}}` - مبلغ الضريبة
- `{{total_amount}}` - المبلغ الإجمالي

### معلومات الدفع
- `{{bank_name}}` - اسم البنك
- `{{account_number}}` - رقم الحساب

### معلومات إضافية
- `{{user_name}}` - اسم المستخدم
- `{{notes}}` - ملاحظات
- `{{invoice_items}}` - عناصر الفاتورة (جدول HTML)

## كيفية إضافة قالب جديد

1. **إنشاء HTML للقالب**:
   ```html
   <div class="my-template">
       <h1>{{company_name}}</h1>
       <p>Invoice: {{invoice_number}}</p>
       <!-- باقي محتوى القالب -->
   </div>
   ```

2. **إنشاء CSS للقالب**:
   ```css
   .my-template {
       font-family: Arial, sans-serif;
       max-width: 800px;
       margin: 0 auto;
   }
   ```

3. **إدراج القالب في قاعدة البيانات**:
   ```sql
   INSERT INTO invoice_templates (name, description, category, html_template, css_styles, is_active)
   VALUES ('اسم القالب', 'وصف القالب', 'modern', 'HTML_CONTENT', 'CSS_CONTENT', 1);
   ```

## الملفات ذات الصلة

- `templates.php` - معرض القوالب
- `ajax/preview-template.php` - معاينة القوالب
- `ajax/live-preview.php` - المعاينة المباشرة
- `create-invoice.php` - إنشاء الفواتير باستخدام القوالب

## ملاحظات مهمة

1. **الاستجابة**: جميع القوالب مصممة لتكون متجاوبة مع جميع أحجام الشاشات
2. **الطباعة**: القوالب محسنة للطباعة مع إعدادات CSS خاصة
3. **الأمان**: جميع المتغيرات يتم تنظيفها قبل الإدراج لمنع XSS
4. **الأداء**: القوالب محسنة للأداء مع CSS مضغوط

## التحديثات الأخيرة

- **2024**: إضافة القالب الحديث المتطور مع تصميم مستوحى من أفق المدن
- **2024**: إضافة القالب الهندسي الحديث مع أشكال هندسية وألوان زرقاء متدرجة
- **2024**: إضافة القالب المتدرج البنفسجي مع تدرج لوني رائع ودوائر متداخلة
- **2024**: إضافة القالب الأزرق الأنيق مع شعار دائري وتصميم احترافي
- تحسين نظام المعاينة المباشرة مع دعم البيانات المخصصة لكل قالب
- إضافة دعم لمتغيرات جديدة وتنسيقات متنوعة
- تحسين الاستجابة على الأجهزة المحمولة
- إصلاح التحذيرات في ملفات المعاينة
- تحسين تنسيق العناصر في الجداول
- إضافة نظام معاينة ذكي يتكيف مع كل قالب

---

للمزيد من المساعدة أو لإضافة قوالب جديدة، يرجى مراجعة الوثائق الفنية أو الاتصال بفريق التطوير.
