<?php
require_once 'config/config.php';
require_once 'includes/SimpleExcelReader.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

try {
    // إعداد العناوين
    $headers = ['name', 'description', 'price', 'quantity', 'category', 'sku', 'status'];
    
    // العناوين التوضيحية بالعربية
    $arabic_headers = [
        'اسم المنتج (مطلوب)',
        'وصف المنتج (اختياري)', 
        'السعر (مطلوب)',
        'الكمية (مطلوب)',
        'الفئة (اختياري)',
        'رمز المنتج (اختياري)',
        'الحالة (active/inactive)'
    ];
    
    // بيانات تجريبية متنوعة ومحسنة
    $sample_data = [
        ['لابتوب Dell Inspiron 15', 'لابتوب عالي الأداء مع معالج Intel Core i7 وذاكرة 16GB RAM وقرص SSD 512GB', 2500.00, 10, 'إلكترونيات', 'DELL-INS-15-001', 'active'],
        ['هاتف Samsung Galaxy S23', 'هاتف ذكي بكاميرا عالية الدقة 108MP وشاشة AMOLED 6.1 بوصة', 3200.00, 25, 'إلكترونيات', 'SAM-GAL-S23-001', 'active'],
        ['كتاب تعلم البرمجة', 'دليل شامل لتعلم البرمجة للمبتدئين مع أمثلة عملية', 150.00, 50, 'كتب', 'BOOK-PROG-001', 'active'],
        ['قميص قطني أزرق', 'قميص قطني عالي الجودة مقاس L لون أزرق فاتح', 85.00, 30, 'ملابس', 'SHIRT-BLUE-L-001', 'inactive'],
        ['ساعة ذكية Apple Watch', 'ساعة ذكية مع مراقب معدل ضربات القلب وGPS وشاشة Retina', 1800.00, 15, 'إلكترونيات', 'APPLE-WATCH-001', 'active'],
        ['كوب قهوة سيراميك', 'كوب قهوة أنيق من السيراميك الفاخر بسعة 350 مل', 45.00, 100, 'أدوات منزلية', 'CUP-CERAMIC-001', 'active'],
        ['حقيبة ظهر رياضية', 'حقيبة ظهر مقاومة للماء للأنشطة الرياضية بسعة 30 لتر', 120.00, 40, 'رياضة', 'BAG-SPORT-001', 'active'],
        ['سماعات لاسلكية', 'سماعات بلوتوث عالية الجودة مع إلغاء الضوضاء وبطارية 30 ساعة', 450.00, 20, 'إلكترونيات', 'HEADPHONES-BT-001', 'active'],
        ['طاولة مكتب خشبية', 'طاولة مكتب من الخشب الطبيعي بتصميم عصري 120x60 سم', 750.00, 8, 'أثاث', 'DESK-WOOD-001', 'active'],
        ['مصباح LED ذكي', 'مصباح LED قابل للتحكم عبر التطبيق مع ألوان متعددة وتوقيت', 95.00, 60, 'إلكترونيات', 'LED-SMART-001', 'active']
    ];
    
    // إنشاء محتوى Excel باستخدام SimpleExcelWriter
    $writer = new SimpleExcelWriter();
    
    // إضافة العناوين الإنجليزية
    $writer->addRow($headers);
    
    // إضافة العناوين العربية كتعليق
    $writer->addRow($arabic_headers);
    
    // إضافة صف فارغ للفصل
    $writer->addRow(['', '', '', '', '', '', '']);
    
    // إضافة البيانات التجريبية
    foreach ($sample_data as $data) {
        $writer->addRow($data);
    }
    
    // إضافة ملاحظات مهمة
    $writer->addRow(['', '', '', '', '', '', '']);
    $writer->addRow(['=== ملاحظات مهمة ===', '', '', '', '', '', '']);
    $writer->addRow(['1. الأعمدة المطلوبة: name, price, quantity', '', '', '', '', '', '']);
    $writer->addRow(['2. الأعمدة الاختيارية: description, category, sku, status', '', '', '', '', '', '']);
    $writer->addRow(['3. قيم الحالة المقبولة: active, inactive, نشط, غير نشط', '', '', '', '', '', '']);
    $writer->addRow(['4. السعر والكمية يجب أن تكون أرقام موجبة', '', '', '', '', '', '']);
    $writer->addRow(['5. رمز المنتج (SKU) يجب أن يكون فريد لكل مستخدم', '', '', '', '', '', '']);
    $writer->addRow(['6. احذف هذه الصفوف والملاحظات قبل رفع الملف', '', '', '', '', '', '']);
    $writer->addRow(['7. يمكنك حفظ الملف بصيغة Excel (.xlsx) أو CSV (UTF-8)', '', '', '', '', '', '']);
    $writer->addRow(['8. استخدم الفاصلة (,) كفاصل بين الأعمدة في CSV', '', '', '', '', '', '']);
    $writer->addRow(['', '', '', '', '', '', '']);
    $writer->addRow(['=== أمثلة على القيم المقبولة ===', '', '', '', '', '', '']);
    $writer->addRow(['الحالة: active, inactive, نشط, غير نشط', '', '', '', '', '', '']);
    $writer->addRow(['السعر: 100, 150.50, 2500.00 (بدون رموز عملة)', '', '', '', '', '', '']);
    $writer->addRow(['الكمية: 1, 10, 100 (أرقام صحيحة فقط)', '', '', '', '', '', '']);
    $writer->addRow(['الفئة: إلكترونيات, كتب, ملابس, أثاث', '', '', '', '', '', '']);
    $writer->addRow(['رمز المنتج: PROD-001, LAPTOP-DELL-001', '', '', '', '', '', '']);
    $writer->addRow(['', '', '', '', '', '', '']);
    $writer->addRow(['=== تعليمات الاستخدام ===', '', '', '', '', '', '']);
    $writer->addRow(['1. احذف جميع الصفوف من هذا الصف وما بعده', '', '', '', '', '', '']);
    $writer->addRow(['2. احتفظ بالصف الأول (أسماء الأعمدة الإنجليزية)', '', '', '', '', '', '']);
    $writer->addRow(['3. يمكنك الاحتفاظ بالصف الثاني (الأسماء العربية) أو حذفه', '', '', '', '', '', '']);
    $writer->addRow(['4. أضف بيانات منتجاتك في الصفوف التالية', '', '', '', '', '', '']);
    $writer->addRow(['5. احفظ الملف وارفعه في صفحة الاستيراد', '', '', '', '', '', '']);
    
    // محاولة إنشاء ملف Excel حقيقي إذا كانت المكتبة متوفرة
    if (file_exists('vendor/autoload.php')) {
        require_once 'vendor/autoload.php';
        if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
            // إنشاء ملف Excel حقيقي
            createRealExcelFile($headers, $arabic_headers, $sample_data);
            exit;
        }
    }

    // إنشاء ملف CSV متوافق مع Excel
    createExcelCompatibleCSV($headers, $arabic_headers, $sample_data);
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، إعادة توجيه مع رسالة خطأ
    setMessage('حدث خطأ في إنشاء النموذج: ' . $e->getMessage(), 'error');
    redirect('import-products.php');
}

/**
 * إنشاء ملف Excel حقيقي باستخدام PhpSpreadsheet
 */
function createRealExcelFile($headers, $arabic_headers, $sample_data) {
    require_once 'vendor/autoload.php';

    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // تعيين اتجاه النص من اليمين لليسار
    $sheet->setRightToLeft(true);

    // إعداد العناوين
    $sheet->setTitle('نموذج المنتجات');

    // إضافة العناوين الإنجليزية
    $col = 1;
    foreach ($headers as $header) {
        $sheet->setCellValueByColumnAndRow($col, 1, $header);
        $col++;
    }

    // تنسيق صف العناوين
    $headerRange = 'A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '1';
    $sheet->getStyle($headerRange)->applyFromArray([
        'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
        'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'color' => ['rgb' => '4472C4']],
        'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]
    ]);

    // إضافة العناوين العربية
    $col = 1;
    foreach ($arabic_headers as $header) {
        $sheet->setCellValueByColumnAndRow($col, 2, $header);
        $col++;
    }

    // تنسيق صف العناوين العربية
    $arabicRange = 'A2:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '2';
    $sheet->getStyle($arabicRange)->applyFromArray([
        'font' => ['italic' => true, 'color' => ['rgb' => '666666']],
        'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'color' => ['rgb' => 'F2F2F2']],
        'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER]
    ]);

    // إضافة صف فارغ
    $currentRow = 4;

    // إضافة البيانات التجريبية
    foreach ($sample_data as $data) {
        $col = 1;
        foreach ($data as $value) {
            $sheet->setCellValueByColumnAndRow($col, $currentRow, $value);
            $col++;
        }
        $currentRow++;
    }

    // تنسيق البيانات
    $dataRange = 'A4:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . ($currentRow - 1);
    $sheet->getStyle($dataRange)->applyFromArray([
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                'color' => ['rgb' => 'CCCCCC']
            ]
        ]
    ]);

    // ضبط عرض الأعمدة تلقائياً
    foreach (range('A', \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers))) as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // إعداد اسم الملف
    $filename = 'نموذج_المنتجات_Excel_' . date('Y-m-d_H-i-s') . '.xlsx';

    // إعداد headers للتحميل
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // كتابة الملف
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save('php://output');
}

/**
 * إنشاء ملف CSV متوافق مع Excel
 */
function createExcelCompatibleCSV($headers, $arabic_headers, $sample_data) {
    // إعداد اسم الملف كـ CSV
    $filename = 'نموذج_المنتجات_' . date('Y-m-d_H-i-s') . '.csv';

    // إعداد headers للتحميل
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // إنشاء output
    $output = fopen('php://output', 'w');

    // إضافة BOM للدعم الصحيح للعربية في Excel
    fwrite($output, "\xEF\xBB\xBF");

    // إضافة العناوين الإنجليزية
    fputcsv($output, $headers);

    // إضافة العناوين العربية كتعليق
    fputcsv($output, $arabic_headers);

    // إضافة صف فارغ
    fputcsv($output, array_fill(0, count($headers), ''));

    // إضافة البيانات التجريبية
    foreach ($sample_data as $data) {
        fputcsv($output, $data);
    }

    // إضافة ملاحظات
    fputcsv($output, array_fill(0, count($headers), ''));
    fputcsv($output, ['=== ملاحظات مهمة ===', '', '', '', '', '', '']);
    fputcsv($output, ['1. الأعمدة المطلوبة: name, price, quantity', '', '', '', '', '', '']);
    fputcsv($output, ['2. الأعمدة الاختيارية: description, category, sku, status', '', '', '', '', '', '']);
    fputcsv($output, ['3. قيم الحالة المقبولة: active, inactive, نشط, غير نشط', '', '', '', '', '', '']);
    fputcsv($output, ['4. السعر والكمية يجب أن تكون أرقام موجبة', '', '', '', '', '', '']);
    fputcsv($output, ['5. رمز المنتج (SKU) يجب أن يكون فريد لكل مستخدم', '', '', '', '', '', '']);
    fputcsv($output, ['6. احذف هذه الصفوف والملاحظات قبل رفع الملف', '', '', '', '', '', '']);
    fputcsv($output, ['7. يمكنك فتح هذا الملف في Excel وحفظه بصيغة .xlsx', '', '', '', '', '', '']);

    fclose($output);
}
?>
