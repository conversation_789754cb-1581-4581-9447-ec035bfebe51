<?php
/**
 * تنظيف SKU المكررة في جدول المنتجات
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تنظيف SKU المكررة</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧹 تنظيف SKU المكررة</h1>";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// دالة إنشاء SKU فريد
function generateCleanSKU($pdo, $user_id, $product_name, $existing_sku = '') {
    $base_sku = '';
    
    // إذا كان SKU موجود، استخدمه كأساس
    if (!empty($existing_sku) && $existing_sku != '') {
        $base_sku = preg_replace('/[^a-zA-Z0-9]/', '', $existing_sku);
        if (strlen($base_sku) < 3) {
            $base_sku = 'PRD';
        }
    } else {
        // إنشاء من اسم المنتج
        $clean_name = trim($product_name);
        $english_chars = preg_replace('/[^a-zA-Z0-9]/', '', $clean_name);
        if (strlen($english_chars) >= 3) {
            $base_sku = strtoupper(substr($english_chars, 0, 3));
        } else {
            $base_sku = 'PRD';
        }
    }
    
    $timestamp = substr(time(), -6);
    $counter = 1;
    
    do {
        $sku = $base_sku . '-' . $timestamp . str_pad($counter, 2, '0', STR_PAD_LEFT);
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ? AND sku = ?");
        $stmt->execute([$user_id, $sku]);
        $exists = $stmt->fetchColumn() > 0;
        
        if (!$exists) {
            return $sku;
        }
        
        $counter++;
    } while ($counter <= 99);
    
    return 'PRD-' . substr(uniqid(), -8);
}

// 1. فحص SKU المكررة
echo "<h2>1. فحص SKU المكررة</h2>";

try {
    // البحث عن SKU مكررة
    $stmt = $pdo->query("
        SELECT user_id, sku, COUNT(*) as count, GROUP_CONCAT(id) as product_ids
        FROM products 
        WHERE sku IS NOT NULL AND sku != '' 
        GROUP BY user_id, sku 
        HAVING count > 1
        ORDER BY user_id, sku
    ");
    $duplicates = $stmt->fetchAll();
    
    if (empty($duplicates)) {
        echo "<div class='result success'>✅ لا توجد SKU مكررة</div>";
    } else {
        echo "<div class='result warning'>⚠️ تم العثور على " . count($duplicates) . " SKU مكررة</div>";
        
        echo "<table>";
        echo "<tr><th>المستخدم</th><th>SKU</th><th>عدد التكرار</th><th>معرفات المنتجات</th></tr>";
        foreach ($duplicates as $dup) {
            echo "<tr>";
            echo "<td>" . $dup['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($dup['sku']) . "</td>";
            echo "<td>" . $dup['count'] . "</td>";
            echo "<td>" . $dup['product_ids'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص SKU المكررة: " . $e->getMessage() . "</div>";
}

// 2. فحص SKU الفارغة
echo "<h2>2. فحص SKU الفارغة</h2>";

try {
    $stmt = $pdo->query("
        SELECT user_id, COUNT(*) as count, GROUP_CONCAT(id) as product_ids
        FROM products 
        WHERE sku IS NULL OR sku = '' 
        GROUP BY user_id
    ");
    $empty_skus = $stmt->fetchAll();
    
    if (empty($empty_skus)) {
        echo "<div class='result success'>✅ لا توجد منتجات بدون SKU</div>";
    } else {
        echo "<div class='result info'>📋 منتجات بدون SKU:</div>";
        
        echo "<table>";
        echo "<tr><th>المستخدم</th><th>عدد المنتجات</th><th>معرفات المنتجات</th></tr>";
        foreach ($empty_skus as $empty) {
            echo "<tr>";
            echo "<td>" . $empty['user_id'] . "</td>";
            echo "<td>" . $empty['count'] . "</td>";
            echo "<td>" . substr($empty['product_ids'], 0, 50) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص SKU الفارغة: " . $e->getMessage() . "</div>";
}

// 3. إصلاح SKU المكررة
if (isset($_GET['fix_duplicates']) && $_GET['fix_duplicates'] == '1') {
    echo "<h2>3. إصلاح SKU المكررة</h2>";
    
    try {
        $fixed_count = 0;
        
        foreach ($duplicates as $dup) {
            $product_ids = explode(',', $dup['product_ids']);
            $user_id = $dup['user_id'];
            $original_sku = $dup['sku'];
            
            // الاحتفاظ بأول منتج كما هو، وتغيير الباقي
            for ($i = 1; $i < count($product_ids); $i++) {
                $product_id = trim($product_ids[$i]);
                
                // الحصول على اسم المنتج
                $stmt = $pdo->prepare("SELECT name FROM products WHERE id = ?");
                $stmt->execute([$product_id]);
                $product = $stmt->fetch();
                
                if ($product) {
                    $new_sku = generateCleanSKU($pdo, $user_id, $product['name'], $original_sku);
                    
                    // تحديث SKU
                    $stmt = $pdo->prepare("UPDATE products SET sku = ? WHERE id = ?");
                    $stmt->execute([$new_sku, $product_id]);
                    
                    $fixed_count++;
                    echo "<div class='result info'>تم تغيير SKU للمنتج ID $product_id من '$original_sku' إلى '$new_sku'</div>";
                }
            }
        }
        
        echo "<div class='result success'>✅ تم إصلاح $fixed_count منتج</div>";
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إصلاح SKU المكررة: " . $e->getMessage() . "</div>";
    }
}

// 4. إصلاح SKU الفارغة
if (isset($_GET['fix_empty']) && $_GET['fix_empty'] == '1') {
    echo "<h2>4. إصلاح SKU الفارغة</h2>";
    
    try {
        $stmt = $pdo->query("SELECT id, user_id, name FROM products WHERE sku IS NULL OR sku = ''");
        $empty_products = $stmt->fetchAll();
        
        $fixed_count = 0;
        
        foreach ($empty_products as $product) {
            $new_sku = generateCleanSKU($pdo, $product['user_id'], $product['name']);
            
            $stmt = $pdo->prepare("UPDATE products SET sku = ? WHERE id = ?");
            $stmt->execute([$new_sku, $product['id']]);
            
            $fixed_count++;
            
            if ($fixed_count <= 10) { // عرض أول 10 فقط
                echo "<div class='result info'>تم إنشاء SKU للمنتج '" . htmlspecialchars($product['name']) . "': $new_sku</div>";
            }
        }
        
        echo "<div class='result success'>✅ تم إنشاء SKU لـ $fixed_count منتج</div>";
        
    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في إصلاح SKU الفارغة: " . $e->getMessage() . "</div>";
    }
}

// أزرار الإجراءات
if (!isset($_GET['fix_duplicates']) && !isset($_GET['fix_empty'])) {
    echo "<h2>الإجراءات المتاحة</h2>";
    
    if (!empty($duplicates)) {
        echo "<a href='?fix_duplicates=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 إصلاح SKU المكررة</a>";
    }
    
    if (!empty($empty_skus)) {
        echo "<a href='?fix_empty=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>✨ إنشاء SKU للمنتجات الفارغة</a>";
    }
    
    if (empty($duplicates) && empty($empty_skus)) {
        echo "<div class='result success'>🎉 جميع المنتجات لديها SKU فريدة!</div>";
    }
}

echo "<div style='margin: 20px 0;'>";
echo "<a href='import-products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى استيراد المنتجات</a> ";
echo "<a href='debug-products-import.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تشخيص شامل</a>";
echo "</div>";

echo "</body></html>";
?>
