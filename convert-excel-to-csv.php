<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$page_title = 'تحويل Excel إلى CSV';
include 'includes/header.php';
?>

<div class="container py-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>تحويل ملف Excel إلى CSV
                    </h5>
                </div>
                <div class="card-body">
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])): ?>
                        
                        <?php
                        try {
                            $file = $_FILES['excel_file'];
                            
                            if ($file['error'] !== UPLOAD_ERR_OK) {
                                throw new Exception('خطأ في رفع الملف');
                            }
                            
                            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                            if (!in_array($extension, ['xlsx', 'xls'])) {
                                throw new Exception('يرجى رفع ملف Excel (.xlsx أو .xls)');
                            }
                            
                            echo "<div class='alert alert-info'>";
                            echo "<h6><i class='fas fa-info-circle me-2'></i>معلومات الملف:</h6>";
                            echo "<p><strong>اسم الملف:</strong> " . htmlspecialchars($file['name']) . "</p>";
                            echo "<p><strong>حجم الملف:</strong> " . number_format($file['size']) . " بايت</p>";
                            echo "<p><strong>نوع الملف:</strong> " . $extension . "</p>";
                            echo "</div>";
                            
                            // محاولة قراءة الملف
                            $data = [];
                            $success = false;
                            
                            // محاولة 1: استخدام PhpSpreadsheet إذا كانت متوفرة
                            if (file_exists('vendor/autoload.php')) {
                                require_once 'vendor/autoload.php';
                                
                                if (class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
                                    try {
                                        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($file['tmp_name']);
                                        $worksheet = $spreadsheet->getActiveSheet();
                                        $data = $worksheet->toArray();
                                        $success = true;
                                        
                                        echo "<div class='alert alert-success'>";
                                        echo "<h6><i class='fas fa-check-circle me-2'></i>تم قراءة ملف Excel بنجاح!</h6>";
                                        echo "<p>عدد الصفوف: <strong>" . count($data) . "</strong></p>";
                                        echo "</div>";
                                        
                                    } catch (Exception $e) {
                                        echo "<div class='alert alert-warning'>";
                                        echo "<p>فشل في قراءة Excel: " . $e->getMessage() . "</p>";
                                        echo "</div>";
                                    }
                                }
                            }
                            
                            if (!$success) {
                                throw new Exception('
                                    <div class="alert alert-danger">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i>لا يمكن قراءة ملف Excel</h6>
                                        <p>النظام لا يستطيع قراءة ملفات Excel مباشرة حالياً.</p>
                                        <p><strong>الحل:</strong></p>
                                        <ol>
                                            <li>افتح الملف في Microsoft Excel أو Google Sheets</li>
                                            <li>اختر "حفظ باسم" أو "Save As"</li>
                                            <li>اختر تنسيق "CSV (UTF-8)" أو "CSV (Comma delimited)"</li>
                                            <li>احفظ الملف</li>
                                            <li>ارفع ملف CSV الجديد في صفحة الاستيراد</li>
                                        </ol>
                                    </div>
                                ');
                            }
                            
                            // إنشاء ملف CSV
                            if ($success && !empty($data)) {
                                $csv_filename = 'converted_' . pathinfo($file['name'], PATHINFO_FILENAME) . '.csv';
                                $csv_path = sys_get_temp_dir() . '/' . $csv_filename;
                                
                                $csv_file = fopen($csv_path, 'w');
                                
                                // إضافة BOM للدعم الصحيح للعربية
                                fwrite($csv_file, "\xEF\xBB\xBF");
                                
                                foreach ($data as $row) {
                                    // تنظيف البيانات
                                    $cleaned_row = array_map(function($cell) {
                                        if ($cell === null) return '';
                                        return (string)$cell;
                                    }, $row);
                                    
                                    fputcsv($csv_file, $cleaned_row);
                                }
                                
                                fclose($csv_file);
                                
                                // عرض رابط التحميل
                                echo "<div class='alert alert-success'>";
                                echo "<h6><i class='fas fa-download me-2'></i>تم إنشاء ملف CSV بنجاح!</h6>";
                                echo "<p>يمكنك الآن تحميل الملف المحول:</p>";
                                echo "<a href='download-converted-csv.php?file=" . urlencode($csv_filename) . "' class='btn btn-success'>";
                                echo "<i class='fas fa-download me-2'></i>تحميل ملف CSV";
                                echo "</a>";
                                echo "</div>";
                                
                                // عرض معاينة البيانات
                                echo "<div class='card mt-3'>";
                                echo "<div class='card-header'>";
                                echo "<h6><i class='fas fa-eye me-2'></i>معاينة البيانات (أول 5 صفوف)</h6>";
                                echo "</div>";
                                echo "<div class='card-body'>";
                                echo "<div class='table-responsive'>";
                                echo "<table class='table table-bordered table-sm'>";
                                
                                for ($i = 0; $i < min(5, count($data)); $i++) {
                                    echo "<tr>";
                                    foreach ($data[$i] as $cell) {
                                        if ($i === 0) {
                                            echo "<th>" . htmlspecialchars($cell) . "</th>";
                                        } else {
                                            echo "<td>" . htmlspecialchars($cell) . "</td>";
                                        }
                                    }
                                    echo "</tr>";
                                }
                                
                                echo "</table>";
                                echo "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo $e->getMessage();
                            echo "</div>";
                        }
                        ?>
                        
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="excel_file" class="form-label">اختر ملف Excel</label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">
                                الملفات المدعومة: .xlsx, .xls | الحد الأقصى: 5 ميجابايت
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-exchange-alt me-2"></i>تحويل إلى CSV
                        </button>
                    </form>
                    
                    <hr>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>كيفية الاستخدام:</h6>
                        <ol>
                            <li>ارفع ملف Excel الخاص بك</li>
                            <li>سيتم تحويله تلقائياً إلى CSV</li>
                            <li>حمل ملف CSV المحول</li>
                            <li>استخدم ملف CSV في صفحة الاستيراد</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>ملاحظة مهمة:</h6>
                        <p class="mb-0">إذا لم يعمل التحويل، يمكنك تحويل الملف يدوياً:</p>
                        <ol class="mb-0">
                            <li>افتح ملف Excel في Microsoft Excel</li>
                            <li>اختر "حفظ باسم" → "CSV (UTF-8)"</li>
                            <li>استخدم الملف المحفوظ في صفحة الاستيراد</li>
                        </ol>
                    </div>
                    
                    <div class="mt-3">
                        <a href="import-products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة للاستيراد
                        </a>
                        <a href="download-simple-excel-template.php" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>تحميل النموذج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
