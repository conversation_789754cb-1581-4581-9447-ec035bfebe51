<?php
/**
 * تشخيص مشاكل استيراد المنتجات
 */

require_once 'config/database.php';
require_once 'includes/auth.php';

// بدء الجلسة
session_start();

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص استيراد المنتجات</title>";
echo "<style>";
echo "body { font-family: Arial; margin: 20px; background: #f8f9fa; }";
echo ".result { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; color: #155724; }";
echo ".error { background: #f8d7da; color: #721c24; }";
echo ".info { background: #d1ecf1; color: #0c5460; }";
echo ".warning { background: #fff3cd; color: #856404; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "table { width: 100%; border-collapse: collapse; margin: 10px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 تشخيص مشاكل استيراد المنتجات</h1>";

try {
    $database = new Database();
    $pdo = $database->getConnection();
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات</div>";
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في الاتصال: " . $e->getMessage() . "</div>";
    exit;
}

// 1. فحص جدول المنتجات
echo "<h2>1. فحص جدول المنتجات</h2>";

try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'products'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='result error'>❌ جدول المنتجات غير موجود</div>";
        exit;
    }
    
    echo "<div class='result success'>✅ جدول المنتجات موجود</div>";
    
    // فحص هيكل الجدول
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll();
    
    echo "<div class='result info'>";
    echo "<strong>هيكل جدول المنتجات:</strong><br>";
    echo "<table>";
    echo "<tr><th>العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>الافتراضي</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // فحص القيود
    $stmt = $pdo->query("SHOW INDEX FROM products");
    $indexes = $stmt->fetchAll();
    
    echo "<div class='result info'>";
    echo "<strong>الفهارس والقيود:</strong><br>";
    foreach ($indexes as $index) {
        echo "- " . $index['Key_name'] . " على العمود " . $index['Column_name'];
        if ($index['Non_unique'] == 0) {
            echo " (فريد)";
        }
        echo "<br>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</div>";
}

// 2. فحص البيانات الموجودة
echo "<h2>2. فحص البيانات الموجودة</h2>";

try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $total = $stmt->fetchColumn();
    echo "<div class='result info'>إجمالي المنتجات: $total</div>";
    
    if ($total > 0) {
        // توزيع المنتجات على المستخدمين
        $stmt = $pdo->query("SELECT user_id, COUNT(*) as count FROM products GROUP BY user_id");
        $distribution = $stmt->fetchAll();
        
        echo "<div class='result info'>";
        echo "<strong>توزيع المنتجات على المستخدمين:</strong><br>";
        foreach ($distribution as $row) {
            echo "- المستخدم ID " . $row['user_id'] . ": " . $row['count'] . " منتج<br>";
        }
        echo "</div>";
        
        // فحص SKU المكررة
        $stmt = $pdo->query("
            SELECT user_id, sku, COUNT(*) as count 
            FROM products 
            WHERE sku IS NOT NULL AND sku != '' 
            GROUP BY user_id, sku 
            HAVING count > 1
        ");
        $duplicates = $stmt->fetchAll();
        
        if (!empty($duplicates)) {
            echo "<div class='result warning'>";
            echo "<strong>⚠️ SKU مكررة موجودة:</strong><br>";
            foreach ($duplicates as $dup) {
                echo "- المستخدم " . $dup['user_id'] . ", SKU: '" . $dup['sku'] . "' (" . $dup['count'] . " مرات)<br>";
            }
            echo "</div>";
        } else {
            echo "<div class='result success'>✅ لا توجد SKU مكررة</div>";
        }
        
        // فحص SKU الفارغة
        $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE sku IS NULL OR sku = ''");
        $empty_sku = $stmt->fetchColumn();
        echo "<div class='result info'>المنتجات بدون SKU: $empty_sku</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في فحص البيانات: " . $e->getMessage() . "</div>";
}

// 3. اختبار إدراج منتج تجريبي
echo "<h2>3. اختبار إدراج منتج تجريبي</h2>";

try {
    // الحصول على مستخدم للاختبار
    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "<div class='result error'>❌ لا يوجد مستخدمين نشطين</div>";
    } else {
        $test_user_id = $user['id'];
        echo "<div class='result info'>سيتم الاختبار مع المستخدم ID: $test_user_id</div>";
        
        // محاولة إدراج منتج تجريبي
        $test_sku = 'TEST-' . time();
        $stmt = $pdo->prepare("
            INSERT INTO products (user_id, name, description, price, quantity, category, sku, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $success = $stmt->execute([
            $test_user_id,
            'منتج تجريبي',
            'وصف تجريبي',
            100.00,
            5,
            'فئة تجريبية',
            $test_sku,
            'active'
        ]);
        
        if ($success) {
            $product_id = $pdo->lastInsertId();
            echo "<div class='result success'>✅ تم إدراج منتج تجريبي بنجاح (ID: $product_id, SKU: $test_sku)</div>";
            
            // حذف المنتج التجريبي
            $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            echo "<div class='result info'>تم حذف المنتج التجريبي</div>";
        } else {
            echo "<div class='result error'>❌ فشل في إدراج المنتج التجريبي</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار الإدراج: " . $e->getMessage() . "</div>";
}

// 4. اختبار دالة إنشاء SKU
echo "<h2>4. اختبار دالة إنشاء SKU</h2>";

// تضمين الدالة
include_once 'import-products.php';

try {
    if (function_exists('generateUniqueSKU')) {
        $test_user_id = $user['id'] ?? 1;
        $test_sku1 = generateUniqueSKU($pdo, $test_user_id, 'منتج تجريبي');
        $test_sku2 = generateUniqueSKU($pdo, $test_user_id, 'Product Test');
        $test_sku3 = generateUniqueSKU($pdo, $test_user_id, '123 Product');
        
        echo "<div class='result success'>";
        echo "✅ دالة إنشاء SKU تعمل بنجاح:<br>";
        echo "- 'منتج تجريبي' → $test_sku1<br>";
        echo "- 'Product Test' → $test_sku2<br>";
        echo "- '123 Product' → $test_sku3<br>";
        echo "</div>";
    } else {
        echo "<div class='result error'>❌ دالة generateUniqueSKU غير موجودة</div>";
    }
} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ في اختبار دالة SKU: " . $e->getMessage() . "</div>";
}

echo "<h2>5. التوصيات</h2>";
echo "<div class='result info'>";
echo "<strong>للحصول على أفضل النتائج:</strong><br>";
echo "1. تأكد من أن ملف الاستيراد يستخدم الترميز UTF-8<br>";
echo "2. استخدم الفواصل المنقوطة (;) أو الفواصل العادية (,) بشكل ثابت<br>";
echo "3. تأكد من أن العناوين في السطر الأول تطابق: name, price, quantity<br>";
echo "4. يمكن ترك عمود SKU فارغاً - سيتم إنشاؤه تلقائياً<br>";
echo "5. تأكد من أن الأسعار والكميات أرقام صحيحة<br>";
echo "</div>";

echo "<div style='margin: 20px 0;'>";
echo "<a href='import-products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى استيراد المنتجات</a>";
echo "</div>";

echo "</body></html>";
?>
