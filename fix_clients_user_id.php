<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشكلة user_id في جدول العملاء</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }
        .btn-success { background: #28a745; }
        .btn-primary { background: #007bff; }
        .btn-warning { background: #ffc107; color: #212529; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 إصلاح مشكلة user_id في جدول العملاء</h1>
    <p>هذا السكريبت سيقوم بفحص وإصلاح مشكلة عمود user_id في جدول العملاء</p>
";

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='result success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // فحص وجود جدول العملاء
    echo "<h2>الخطوة 1: فحص وجود جدول العملاء</h2>";
    $stmt = $pdo->query('SHOW TABLES LIKE "clients"');
    
    if ($stmt->rowCount() == 0) {
        echo "<div class='result error'>❌ جدول العملاء غير موجود</div>";
        echo "<div class='result info'>سيتم إنشاء جدول العملاء من الصفر...</div>";
        
        // إنشاء جدول العملاء
        $createTableSql = "
        CREATE TABLE clients (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL DEFAULT 1,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            company VARCHAR(100),
            address TEXT,
            city VARCHAR(50),
            country VARCHAR(50),
            tax_number VARCHAR(50),
            notes TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_email (email),
            INDEX idx_name (name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTableSql);
        echo "<div class='result success'>✅ تم إنشاء جدول العملاء بنجاح</div>";
        
    } else {
        echo "<div class='result success'>✅ جدول العملاء موجود</div>";
        
        // فحص أعمدة الجدول
        echo "<h2>الخطوة 2: فحص أعمدة جدول العملاء</h2>";
        $stmt = $pdo->query('DESCRIBE clients');
        $columns = $stmt->fetchAll();
        
        $hasUserId = false;
        $columnsList = [];
        
        foreach ($columns as $column) {
            $columnsList[] = $column['Field'];
            if ($column['Field'] == 'user_id') {
                $hasUserId = true;
            }
        }
        
        echo "<div class='result info'>";
        echo "<strong>الأعمدة الموجودة:</strong><br>";
        echo implode(', ', $columnsList);
        echo "</div>";
        
        if (!$hasUserId) {
            echo "<div class='result error'>❌ عمود user_id مفقود</div>";
            echo "<div class='result info'>سيتم إضافة عمود user_id...</div>";
            
            // إضافة عمود user_id
            try {
                $pdo->exec("ALTER TABLE clients ADD COLUMN user_id INT NOT NULL DEFAULT 1 AFTER id");
                echo "<div class='result success'>✅ تم إضافة عمود user_id بنجاح</div>";
                
                // إضافة فهرس
                try {
                    $pdo->exec("ALTER TABLE clients ADD INDEX idx_user_id (user_id)");
                    echo "<div class='result success'>✅ تم إضافة فهرس user_id</div>";
                } catch (Exception $e) {
                    echo "<div class='result warning'>⚠️ لم يتم إضافة الفهرس: " . $e->getMessage() . "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='result error'>❌ فشل في إضافة عمود user_id: " . $e->getMessage() . "</div>";
                throw $e;
            }
            
        } else {
            echo "<div class='result success'>✅ عمود user_id موجود</div>";
        }
    }
    
    // التأكد من وجود الأعمدة الإضافية
    echo "<h2>الخطوة 3: التأكد من الأعمدة الإضافية</h2>";
    
    $additionalColumns = [
        'tax_number' => "ALTER TABLE clients ADD COLUMN tax_number VARCHAR(50) DEFAULT NULL",
        'notes' => "ALTER TABLE clients ADD COLUMN notes TEXT DEFAULT NULL",
        'is_active' => "ALTER TABLE clients ADD COLUMN is_active BOOLEAN DEFAULT TRUE"
    ];
    
    foreach ($additionalColumns as $columnName => $sql) {
        try {
            $pdo->exec($sql);
            echo "<div class='result success'>✅ تم إضافة عمود $columnName</div>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='result info'>ℹ️ عمود $columnName موجود بالفعل</div>";
            } else {
                echo "<div class='result warning'>⚠️ مشكلة في عمود $columnName: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // فحص عدد السجلات
    echo "<h2>الخطوة 4: فحص البيانات الموجودة</h2>";
    $stmt = $pdo->query('SELECT COUNT(*) FROM clients');
    $clientCount = $stmt->fetchColumn();
    echo "<div class='result info'>عدد العملاء الموجودين: $clientCount</div>";
    
    if ($clientCount > 0) {
        // التأكد من ربط السجلات بالمستخدمين
        $stmt = $pdo->query('SELECT COUNT(*) FROM clients WHERE user_id = 0 OR user_id IS NULL');
        $unlinkedCount = $stmt->fetchColumn();
        
        if ($unlinkedCount > 0) {
            echo "<div class='result warning'>⚠️ يوجد $unlinkedCount سجل غير مربوط بمستخدم</div>";
            
            // البحث عن أول مستخدم نشط
            $stmt = $pdo->query('SELECT id FROM users WHERE is_active = 1 ORDER BY id ASC LIMIT 1');
            $firstUser = $stmt->fetch();
            
            if ($firstUser) {
                $userId = $firstUser['id'];
                echo "<div class='result info'>سيتم ربط السجلات بالمستخدم ID: $userId</div>";
                
                $stmt = $pdo->prepare('UPDATE clients SET user_id = ? WHERE user_id = 0 OR user_id IS NULL');
                $stmt->execute([$userId]);
                
                echo "<div class='result success'>✅ تم ربط السجلات بالمستخدم</div>";
            } else {
                echo "<div class='result warning'>⚠️ لا يوجد مستخدمين نشطين</div>";
            }
        } else {
            echo "<div class='result success'>✅ جميع السجلات مربوطة بمستخدمين</div>";
        }
    }

    // اختبار الاستعلامات
    echo "<h2>الخطوة 5: اختبار الاستعلامات</h2>";

    try {
        // اختبار استعلام بسيط
        $stmt = $pdo->query('SELECT c.id, c.user_id, c.name FROM clients c WHERE c.user_id > 0 LIMIT 3');
        $results = $stmt->fetchAll();

        echo "<div class='result success'>✅ الاستعلام البسيط يعمل بنجاح</div>";
        echo "<div class='result info'>عدد النتائج: " . count($results) . "</div>";

        if (!empty($results)) {
            echo "<div class='result info'>";
            echo "<strong>عينة من البيانات:</strong><br>";
            foreach ($results as $row) {
                echo "- ID: {$row['id']}, User ID: {$row['user_id']}, Name: " . htmlspecialchars($row['name']) . "<br>";
            }
            echo "</div>";
        }

        // اختبار استعلام معقد مع JOIN
        $complexSql = "
            SELECT c.id, c.user_id, c.name, c.email, c.company,
                   COALESCE(COUNT(i.id), 0) as invoice_count
            FROM clients c
            LEFT JOIN invoices i ON c.id = i.client_id
            WHERE c.user_id > 0 AND c.is_active = 1
            GROUP BY c.id, c.user_id, c.name, c.email, c.company
            LIMIT 3
        ";

        $stmt = $pdo->query($complexSql);
        $complexResults = $stmt->fetchAll();

        echo "<div class='result success'>✅ الاستعلام المعقد مع JOIN يعمل بنجاح</div>";
        echo "<div class='result info'>عدد النتائج: " . count($complexResults) . "</div>";

    } catch (Exception $e) {
        echo "<div class='result error'>❌ خطأ في اختبار الاستعلامات: " . $e->getMessage() . "</div>";
    }

    // النتيجة النهائية
    echo "<h2>🎉 النتيجة النهائية</h2>";
    echo "<div class='result success'>";
    echo "<h3>تم إصلاح جدول العملاء بنجاح!</h3>";
    echo "<p><strong>ما تم عمله:</strong></p>";
    echo "<ul>";
    echo "<li>✅ التأكد من وجود جدول العملاء</li>";
    echo "<li>✅ إضافة/التأكد من وجود عمود user_id</li>";
    echo "<li>✅ إضافة الأعمدة الإضافية المطلوبة</li>";
    echo "<li>✅ ربط السجلات الموجودة بالمستخدمين</li>";
    echo "<li>✅ اختبار الاستعلامات</li>";
    echo "</ul>";
    echo "</div>";

    echo "<h3>اختبار الصفحات:</h3>";
    echo "<a href='clients.php' class='btn btn-success' target='_blank'>🔗 صفحة العملاء</a>";
    echo "<a href='dashboard.php' class='btn btn-primary' target='_blank'>🔗 لوحة التحكم</a>";
    echo "<a href='create-invoice.php' class='btn btn-primary' target='_blank'>🔗 إنشاء فاتورة</a>";

} catch (Exception $e) {
    echo "<div class='result error'>❌ خطأ عام: " . $e->getMessage() . "</div>";
    echo "<div class='result info'>تفاصيل الخطأ: " . $e->getTraceAsString() . "</div>";
}

echo "
</div>
</body>
</html>";
?>
