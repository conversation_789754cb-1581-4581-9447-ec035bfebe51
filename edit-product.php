<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$user = getCurrentUser();
$user_id = $user['id'];

$success = '';
$error = '';
$product = null;

// التحقق من وجود معرف المنتج
$product_id = (int)($_GET['id'] ?? 0);
if (!$product_id) {
    redirect('products.php');
}

try {
    $pdo = getDBConnection();
    
    // جلب بيانات المنتج
    $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND user_id = ?");
    $stmt->execute([$product_id, $user_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        setMessage('المنتج غير موجود أو ليس لديك صلاحية للوصول إليه', 'error');
        redirect('products.php');
    }
    
} catch (Exception $e) {
    setMessage('حدث خطأ في جلب بيانات المنتج: ' . $e->getMessage(), 'error');
    redirect('products.php');
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // جمع البيانات من النموذج
        $form_data = [
            'name' => sanitize($_POST['name'] ?? ''),
            'description' => sanitize($_POST['description'] ?? ''),
            'price' => (float)($_POST['price'] ?? 0),
            'quantity' => (int)($_POST['quantity'] ?? 0),
            'category' => sanitize($_POST['category'] ?? ''),
            'sku' => sanitize($_POST['sku'] ?? ''),
            'status' => $_POST['status'] ?? 'active'
        ];
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($form_data['name'])) {
            $errors[] = 'اسم المنتج مطلوب';
        }
        
        if ($form_data['price'] < 0) {
            $errors[] = 'السعر يجب أن يكون أكبر من أو يساوي صفر';
        }
        
        if ($form_data['quantity'] < 0) {
            $errors[] = 'الكمية يجب أن تكون أكبر من أو يساوي صفر';
        }
        
        if (!empty($form_data['sku']) && $form_data['sku'] != $product['sku']) {
            // التحقق من عدم تكرار رمز المنتج
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE user_id = ? AND sku = ? AND id != ?");
            $stmt->execute([$user_id, $form_data['sku'], $product_id]);
            if ($stmt->fetchColumn() > 0) {
                $errors[] = 'رمز المنتج موجود بالفعل';
            }
        }
        
        if (!in_array($form_data['status'], ['active', 'inactive'])) {
            $form_data['status'] = 'active';
        }
        
        if (!empty($errors)) {
            $error = implode('<br>', $errors);
        } else {
            // تحديث المنتج
            $stmt = $pdo->prepare("
                UPDATE products 
                SET name = ?, description = ?, price = ?, quantity = ?, category = ?, sku = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([
                $form_data['name'],
                $form_data['description'],
                $form_data['price'],
                $form_data['quantity'],
                $form_data['category'],
                $form_data['sku'],
                $form_data['status'],
                $product_id,
                $user_id
            ]);
            
            $success = 'تم تحديث المنتج بنجاح';
            
            // تحديث بيانات المنتج المعروضة
            $product = array_merge($product, $form_data);
            
            // إعادة توجيه إلى صفحة المنتجات بعد 2 ثانية
            header("refresh:2;url=products.php");
        }
        
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// جلب الفئات الموجودة للاقتراح
try {
    $stmt = $pdo->prepare("SELECT DISTINCT category FROM products WHERE user_id = ? AND category IS NOT NULL ORDER BY category");
    $stmt->execute([$user_id]);
    $existing_categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $existing_categories = [];
}

$page_title = 'تعديل المنتج: ' . $product['name'];
include 'includes/header.php';
?>

<div class="container py-4">
    <!-- رسائل النجاح والخطأ -->
    <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <div class="mt-2">
                <small>سيتم توجيهك إلى صفحة المنتجات خلال ثوانٍ...</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-edit text-primary me-2"></i>
                تعديل المنتج
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="products.php">المنتجات</a></li>
                    <li class="breadcrumb-item active">تعديل المنتج</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="products.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة إلى المنتجات
            </a>
        </div>
    </div>

    <!-- نموذج تعديل المنتج -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل بيانات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="productForm">
                        <div class="row">
                            <!-- اسم المنتج -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المنتج <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($product['name']); ?>" 
                                       required maxlength="255">
                                <div class="form-text">أدخل اسم المنتج بوضوح</div>
                            </div>
                            
                            <!-- رمز المنتج -->
                            <div class="col-md-6 mb-3">
                                <label for="sku" class="form-label">رمز المنتج (SKU)</label>
                                <input type="text" class="form-control" id="sku" name="sku" 
                                       value="<?php echo htmlspecialchars($product['sku']); ?>" 
                                       maxlength="100">
                                <div class="form-text">رمز فريد للمنتج (اختياري)</div>
                            </div>
                        </div>
                        
                        <!-- وصف المنتج -->
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" placeholder="أدخل وصف تفصيلي للمنتج"><?php echo htmlspecialchars($product['description']); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <!-- السعر -->
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">السعر (ر.س) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="<?php echo $product['price']; ?>" 
                                           step="0.01" min="0" required>
                                    <span class="input-group-text">ر.س</span>
                                </div>
                            </div>
                            
                            <!-- الكمية -->
                            <div class="col-md-4 mb-3">
                                <label for="quantity" class="form-label">الكمية المتاحة <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="quantity" name="quantity" 
                                       value="<?php echo $product['quantity']; ?>" 
                                       min="0" required>
                            </div>
                            
                            <!-- الحالة -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">حالة المنتج</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo $product['status'] == 'active' ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo $product['status'] == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- الفئة -->
                        <div class="mb-4">
                            <label for="category" class="form-label">فئة المنتج</label>
                            <input type="text" class="form-control" id="category" name="category" 
                                   value="<?php echo htmlspecialchars($product['category']); ?>" 
                                   list="categories" maxlength="100">
                            <datalist id="categories">
                                <?php foreach ($existing_categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                <?php endforeach; ?>
                            </datalist>
                            <div class="form-text">اختر فئة موجودة أو أدخل فئة جديدة</div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                                </button>
                                <button type="reset" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                </button>
                            </div>
                            <div>
                                <a href="products.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- معلومات المنتج -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات المنتج
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">تاريخ الإنشاء:</small>
                            <div class="fw-bold"><?php echo date('Y-m-d', strtotime($product['created_at'])); ?></div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">آخر تحديث:</small>
                            <div class="fw-bold"><?php echo date('Y-m-d', strtotime($product['updated_at'])); ?></div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">القيمة الإجمالية:</small>
                            <div class="fw-bold text-success"><?php echo number_format($product['price'] * $product['quantity'], 2); ?> ر.س</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">الحالة الحالية:</small>
                            <div>
                                <span class="badge <?php echo $product['status'] == 'active' ? 'bg-success' : 'bg-warning'; ?>">
                                    <?php echo $product['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نصائح -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>نصائح للتعديل
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <ul class="mb-0 small">
                            <li>تأكد من صحة البيانات قبل الحفظ</li>
                            <li>يمكنك تغيير حالة المنتج لإخفائه مؤقتاً</li>
                            <li>رمز المنتج يجب أن يكون فريد</li>
                            <li>استخدم فئات واضحة للتنظيم</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج قبل الإرسال
document.getElementById('productForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const price = parseFloat(document.getElementById('price').value);
    const quantity = parseInt(document.getElementById('quantity').value);
    
    if (!name) {
        alert('يرجى إدخال اسم المنتج');
        e.preventDefault();
        return;
    }
    
    if (price < 0) {
        alert('السعر يجب أن يكون أكبر من أو يساوي صفر');
        e.preventDefault();
        return;
    }
    
    if (quantity < 0) {
        alert('الكمية يجب أن تكون أكبر من أو يساوي صفر');
        e.preventDefault();
        return;
    }
});
</script>

<?php include 'includes/footer.php'; ?>
