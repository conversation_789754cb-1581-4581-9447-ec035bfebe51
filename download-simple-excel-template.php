<?php
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

try {
    // إعداد العناوين
    $headers = ['name', 'description', 'price', 'quantity', 'category', 'sku', 'status'];
    
    // البيانات التجريبية
    $sample_data = [
        ['لابتوب Dell Inspiron 15', 'لابتوب عالي الأداء مع معالج Intel Core i7', 2500.00, 10, 'إلكترونيات', 'DELL-INS-15-001', 'active'],
        ['هاتف Samsung Galaxy S23', 'هاتف ذكي بكاميرا عالية الدقة 108MP', 3200.00, 25, 'إلكترونيات', 'SAM-GAL-S23-001', 'active'],
        ['كتاب تعلم البرمجة', 'دليل شامل لتعلم البرمجة للمبتدئين', 150.00, 50, 'كتب', 'BOOK-PROG-001', 'active'],
        ['قميص قطني أزرق', 'قميص قطني عالي الجودة مقاس L', 85.00, 30, 'ملابس', 'SHIRT-BLUE-L-001', 'inactive'],
        ['ساعة ذكية Apple Watch', 'ساعة ذكية مع مراقب معدل ضربات القلب', 1800.00, 15, 'إلكترونيات', 'APPLE-WATCH-001', 'active']
    ];
    
    // إنشاء ملف Excel بسيط باستخدام XML
    $filename = 'نموذج_المنتجات_Excel_' . date('Y-m-d_H-i-s') . '.xls';
    
    // إعداد headers للتحميل
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // إضافة BOM للدعم الصحيح للعربية
    echo "\xEF\xBB\xBF";
    
    // بداية ملف Excel XML
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<?mso-application progid="Excel.Sheet"?>' . "\n";
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:o="urn:schemas-microsoft-com:office:office"' . "\n";
    echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"' . "\n";
    echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";
    
    // إعدادات المستند
    echo '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">' . "\n";
    echo '<Title>نموذج المنتجات</Title>' . "\n";
    echo '<Author>نظام إدارة الفواتير</Author>' . "\n";
    echo '<Created>' . date('Y-m-d\TH:i:s\Z') . '</Created>' . "\n";
    echo '</DocumentProperties>' . "\n";
    
    // إعدادات Excel
    echo '<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">' . "\n";
    echo '<WindowHeight>12000</WindowHeight>' . "\n";
    echo '<WindowWidth>18000</WindowWidth>' . "\n";
    echo '<WindowTopX>0</WindowTopX>' . "\n";
    echo '<WindowTopY>0</WindowTopY>' . "\n";
    echo '<ProtectStructure>False</ProtectStructure>' . "\n";
    echo '<ProtectWindows>False</ProtectWindows>' . "\n";
    echo '</ExcelWorkbook>' . "\n";
    
    // الأنماط
    echo '<Styles>' . "\n";
    echo '<Style ss:ID="Default" ss:Name="Normal">' . "\n";
    echo '<Alignment ss:Vertical="Bottom"/>' . "\n";
    echo '<Borders/>' . "\n";
    echo '<Font ss:FontName="Arial" x:Family="Swiss" ss:Size="10"/>' . "\n";
    echo '<Interior/>' . "\n";
    echo '<NumberFormat/>' . "\n";
    echo '<Protection/>' . "\n";
    echo '</Style>' . "\n";
    
    // نمط العناوين
    echo '<Style ss:ID="HeaderStyle">' . "\n";
    echo '<Alignment ss:Horizontal="Center" ss:Vertical="Center"/>' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '<Font ss:FontName="Arial" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF" ss:Bold="1"/>' . "\n";
    echo '<Interior ss:Color="#4472C4" ss:Pattern="Solid"/>' . "\n";
    echo '</Style>' . "\n";
    
    // نمط البيانات
    echo '<Style ss:ID="DataStyle">' . "\n";
    echo '<Alignment ss:Vertical="Center"/>' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#CCCCCC"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#CCCCCC"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#CCCCCC"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#CCCCCC"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '<Font ss:FontName="Arial" x:Family="Swiss" ss:Size="10"/>' . "\n";
    echo '</Style>' . "\n";
    
    echo '</Styles>' . "\n";
    
    // بداية ورقة العمل
    echo '<Worksheet ss:Name="نموذج المنتجات">' . "\n";
    echo '<Table>' . "\n";
    
    // تحديد عرض الأعمدة
    echo '<Column ss:Width="150"/>' . "\n"; // name
    echo '<Column ss:Width="200"/>' . "\n"; // description
    echo '<Column ss:Width="80"/>' . "\n";  // price
    echo '<Column ss:Width="80"/>' . "\n";  // quantity
    echo '<Column ss:Width="100"/>' . "\n"; // category
    echo '<Column ss:Width="120"/>' . "\n"; // sku
    echo '<Column ss:Width="80"/>' . "\n";  // status
    
    // صف العناوين الإنجليزية
    echo '<Row>' . "\n";
    foreach ($headers as $header) {
        echo '<Cell ss:StyleID="HeaderStyle"><Data ss:Type="String">' . htmlspecialchars($header) . '</Data></Cell>' . "\n";
    }
    echo '</Row>' . "\n";
    
    // صف العناوين العربية
    $arabic_headers = [
        'اسم المنتج (مطلوب)',
        'وصف المنتج (اختياري)', 
        'السعر (مطلوب)',
        'الكمية (مطلوب)',
        'الفئة (اختياري)',
        'رمز المنتج (اختياري)',
        'الحالة (active/inactive)'
    ];
    
    echo '<Row>' . "\n";
    foreach ($arabic_headers as $header) {
        echo '<Cell ss:StyleID="DataStyle"><Data ss:Type="String">' . htmlspecialchars($header) . '</Data></Cell>' . "\n";
    }
    echo '</Row>' . "\n";
    
    // صف فارغ
    echo '<Row>' . "\n";
    for ($i = 0; $i < count($headers); $i++) {
        echo '<Cell><Data ss:Type="String"></Data></Cell>' . "\n";
    }
    echo '</Row>' . "\n";
    
    // البيانات التجريبية
    foreach ($sample_data as $row) {
        echo '<Row>' . "\n";
        foreach ($row as $index => $cell) {
            if ($index == 2 || $index == 3) { // price and quantity
                echo '<Cell ss:StyleID="DataStyle"><Data ss:Type="Number">' . htmlspecialchars($cell) . '</Data></Cell>' . "\n";
            } else {
                echo '<Cell ss:StyleID="DataStyle"><Data ss:Type="String">' . htmlspecialchars($cell) . '</Data></Cell>' . "\n";
            }
        }
        echo '</Row>' . "\n";
    }
    
    // إضافة ملاحظات
    echo '<Row>' . "\n";
    for ($i = 0; $i < count($headers); $i++) {
        echo '<Cell><Data ss:Type="String"></Data></Cell>' . "\n";
    }
    echo '</Row>' . "\n";
    
    $notes = [
        '=== ملاحظات مهمة ===',
        '1. الأعمدة المطلوبة: name, price, quantity',
        '2. الأعمدة الاختيارية: description, category, sku, status',
        '3. قيم الحالة المقبولة: active, inactive, نشط, غير نشط',
        '4. السعر والكمية يجب أن تكون أرقام موجبة',
        '5. رمز المنتج (SKU) يجب أن يكون فريد',
        '6. احذف هذه الملاحظات قبل رفع الملف',
        '7. يمكنك حفظ الملف بصيغة .xlsx للحصول على أفضل توافق'
    ];
    
    foreach ($notes as $note) {
        echo '<Row>' . "\n";
        echo '<Cell><Data ss:Type="String">' . htmlspecialchars($note) . '</Data></Cell>' . "\n";
        for ($i = 1; $i < count($headers); $i++) {
            echo '<Cell><Data ss:Type="String"></Data></Cell>' . "\n";
        }
        echo '</Row>' . "\n";
    }
    
    // نهاية ورقة العمل
    echo '</Table>' . "\n";
    echo '</Worksheet>' . "\n";
    echo '</Workbook>' . "\n";
    
} catch (Exception $e) {
    // في حالة حدوث خطأ، إعادة توجيه مع رسالة خطأ
    setMessage('حدث خطأ في إنشاء النموذج: ' . $e->getMessage(), 'error');
    redirect('import-products.php');
}
?>
