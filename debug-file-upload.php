<?php
require_once 'config/config.php';
require_once 'includes/SafeFileReader.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(APP_URL . '/auth/login.php');
}

$page_title = 'تشخيص رفع الملفات';
include 'includes/header.php';
?>

<div class="container py-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>تشخيص مشاكل رفع الملفات
                    </h5>
                </div>
                <div class="card-body">
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])): ?>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>تفاصيل الملف المرفوع:</h6>
                            
                            <?php
                            $file = $_FILES['test_file'];
                            
                            echo "<table class='table table-sm'>";
                            echo "<tr><td><strong>اسم الملف:</strong></td><td>" . htmlspecialchars($file['name']) . "</td></tr>";
                            echo "<tr><td><strong>نوع MIME:</strong></td><td>" . htmlspecialchars($file['type']) . "</td></tr>";
                            echo "<tr><td><strong>حجم الملف:</strong></td><td>" . number_format($file['size']) . " بايت</td></tr>";
                            echo "<tr><td><strong>الملف المؤقت:</strong></td><td>" . htmlspecialchars($file['tmp_name']) . "</td></tr>";
                            echo "<tr><td><strong>كود الخطأ:</strong></td><td>" . $file['error'] . "</td></tr>";
                            
                            // تحليل الامتداد
                            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                            echo "<tr><td><strong>الامتداد:</strong></td><td>." . htmlspecialchars($extension) . "</td></tr>";
                            
                            // فحص وجود الملف
                            $file_exists = file_exists($file['tmp_name']);
                            echo "<tr><td><strong>الملف موجود:</strong></td><td>" . ($file_exists ? "✅ نعم" : "❌ لا") . "</td></tr>";
                            
                            if ($file_exists) {
                                $actual_size = filesize($file['tmp_name']);
                                echo "<tr><td><strong>الحجم الفعلي:</strong></td><td>" . number_format($actual_size) . " بايت</td></tr>";
                                
                                // قراءة أول 100 حرف
                                $sample = file_get_contents($file['tmp_name'], false, null, 0, 100);
                                echo "<tr><td><strong>عينة المحتوى:</strong></td><td><code>" . htmlspecialchars(substr($sample, 0, 50)) . "...</code></td></tr>";
                            }
                            
                            echo "</table>";
                            ?>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-cog me-2"></i>اختبار SafeFileReader:</h6>

                            <?php
                            try {
                                echo "<p>🔍 محاولة إنشاء SafeFileReader...</p>";
                                $reader = new SafeFileReader($file['tmp_name'], $file['name']);
                                echo "<p>✅ تم إنشاء SafeFileReader بنجاح</p>";

                                // عرض معلومات الملف
                                $file_info = $reader->getFileInfo();
                                echo "<p>🔍 نوع الملف المكتشف: <strong>" . $file_info['detected_type'] . "</strong></p>";
                                echo "<p>🔍 يمكن قراءة Excel: <strong>" . ($file_info['can_read_excel'] ? 'نعم' : 'لا') . "</strong></p>";

                                echo "<details><summary>🔧 معلومات تشخيصية تفصيلية</summary>";
                                echo "<pre>" . htmlspecialchars(json_encode($file_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
                                echo "</details>";

                                echo "<p>🔍 محاولة التحقق من صحة الملف...</p>";
                                $reader->validateFile();
                                echo "<p>✅ تم التحقق من صحة الملف بنجاح</p>";

                                echo "<p>🔍 محاولة قراءة الملف...</p>";
                                $data = $reader->readFile();
                                echo "<p>✅ تم قراءة الملف بنجاح</p>";
                                echo "<p>📊 عدد الصفوف: <strong>" . count($data) . "</strong></p>";

                                if (count($data) > 0) {
                                    echo "<p>📋 أول صف:</p>";
                                    echo "<pre>" . htmlspecialchars(print_r($data[0], true)) . "</pre>";

                                    if (count($data) > 1) {
                                        echo "<p>📋 ثاني صف:</p>";
                                        echo "<pre>" . htmlspecialchars(print_r($data[1], true)) . "</pre>";
                                    }
                                }
                                
                            } catch (Exception $e) {
                                echo "<div class='alert alert-danger'>";
                                echo "<p>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
                                echo "</div>";
                                
                                // محاولة تشخيص إضافي
                                echo "<h6>🔧 تشخيص إضافي:</h6>";
                                
                                // فحص الامتداد
                                $allowed_extensions = ['xlsx', 'xls', 'csv'];
                                if (!in_array($extension, $allowed_extensions)) {
                                    echo "<p>❌ الامتداد '.$extension' غير مدعوم</p>";
                                    echo "<p>الامتدادات المدعومة: " . implode(', ', $allowed_extensions) . "</p>";
                                } else {
                                    echo "<p>✅ الامتداد مدعوم</p>";
                                }
                                
                                // فحص المحتوى
                                if ($file_exists && $actual_size > 0) {
                                    $content_sample = file_get_contents($file['tmp_name'], false, null, 0, 20);
                                    echo "<p>🔍 بداية الملف: <code>" . bin2hex($content_sample) . "</code></p>";
                                    
                                    // فحص نوع الملف من المحتوى
                                    if (strpos($content_sample, 'PK') === 0) {
                                        echo "<p>📄 يبدو أنه ملف ZIP/XLSX</p>";
                                    } elseif (strpos($content_sample, '<?xml') !== false) {
                                        echo "<p>📄 يبدو أنه ملف XML</p>";
                                    } elseif (strpos($content_sample, ',') !== false) {
                                        echo "<p>📄 يبدو أنه ملف CSV</p>";
                                    } else {
                                        echo "<p>❓ نوع الملف غير واضح</p>";
                                    }
                                }
                            }
                            ?>
                        </div>
                        
                    <?php endif; ?>
                    
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="test_file" class="form-label">اختر ملف للاختبار</label>
                            <input type="file" class="form-control" id="test_file" name="test_file" 
                                   accept=".xlsx,.xls,.csv" required>
                            <div class="form-text">
                                ارفع أي ملف Excel أو CSV لتشخيص المشكلة
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>تشخيص الملف
                        </button>
                    </form>
                    
                    <hr>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>نصائح لحل المشاكل:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف بصيغة .xlsx أو .xls أو .csv</li>
                            <li>تأكد من أن حجم الملف أقل من 5 ميجابايت</li>
                            <li>جرب تحميل النموذج من النظام واستخدامه</li>
                            <li>إذا كان الملف Excel، جرب حفظه كـ CSV</li>
                            <li>تأكد من أن الملف غير محمي بكلمة مرور</li>
                        </ul>
                    </div>
                    
                    <div class="mt-3">
                        <a href="import-products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة للاستيراد
                        </a>
                        <a href="download-simple-excel-template.php" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>تحميل النموذج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
