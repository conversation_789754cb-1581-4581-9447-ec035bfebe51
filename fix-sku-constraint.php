<?php
/**
 * إصلاح مشكلة قيد التفرد في SKU
 * هذا الملف يحل مشكلة SKU الفارغة المكررة
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $pdo = $database->getConnection();
    
    echo "<h2>إصلاح قيد التفرد في SKU</h2>";
    
    // 1. تحديث القيم الفارغة إلى NULL
    echo "<p>1. تحديث القيم الفارغة إلى NULL...</p>";
    $stmt = $pdo->prepare("UPDATE products SET sku = NULL WHERE sku = '' OR sku IS NULL");
    $updated = $stmt->execute();
    $affected_rows = $stmt->rowCount();
    echo "<p>تم تحديث $affected_rows صف.</p>";
    
    // 2. التحقق من وجود قيد التفرد
    echo "<p>2. التحقق من قيد التفرد الحالي...</p>";
    $stmt = $pdo->query("SHOW INDEX FROM products WHERE Key_name = 'unique_user_sku'");
    $constraint_exists = $stmt->fetch() !== false;
    
    if ($constraint_exists) {
        echo "<p>قيد التفرد موجود بالفعل.</p>";
        
        // 3. إزالة القيد القديم وإعادة إنشاؤه مع دعم NULL
        echo "<p>3. تحديث قيد التفرد...</p>";
        
        try {
            // إزالة القيد القديم
            $pdo->exec("ALTER TABLE products DROP INDEX unique_user_sku");
            echo "<p>تم إزالة القيد القديم.</p>";
        } catch (Exception $e) {
            echo "<p>تحذير: لم يتم العثور على القيد القديم أو حدث خطأ: " . $e->getMessage() . "</p>";
        }
        
        // إنشاء القيد الجديد مع دعم NULL
        $pdo->exec("ALTER TABLE products ADD CONSTRAINT unique_user_sku UNIQUE (user_id, sku)");
        echo "<p>تم إنشاء القيد الجديد بنجاح.</p>";
        
    } else {
        echo "<p>لا يوجد قيد تفرد، سيتم إنشاؤه...</p>";
        $pdo->exec("ALTER TABLE products ADD CONSTRAINT unique_user_sku UNIQUE (user_id, sku)");
        echo "<p>تم إنشاء قيد التفرد بنجاح.</p>";
    }
    
    // 4. التحقق من النتيجة
    echo "<p>4. التحقق من النتيجة...</p>";
    $stmt = $pdo->query("SELECT COUNT(*) as total, COUNT(sku) as with_sku FROM products");
    $result = $stmt->fetch();
    echo "<p>إجمالي المنتجات: " . $result['total'] . "</p>";
    echo "<p>المنتجات مع SKU: " . $result['with_sku'] . "</p>";
    echo "<p>المنتجات بدون SKU: " . ($result['total'] - $result['with_sku']) . "</p>";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم الإصلاح بنجاح!</h3>";
    echo "<p style='margin: 0; color: #155724;'>يمكنك الآن استيراد المنتجات بدون مشاكل في SKU.</p>";
    echo "</div>";
    
    echo "<p><a href='import-products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة إلى استيراد المنتجات</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ حدث خطأ!</h3>";
    echo "<p style='margin: 0; color: #721c24;'>الخطأ: " . $e->getMessage() . "</p>";
    echo "</div>";
    
    echo "<p><strong>الحلول البديلة:</strong></p>";
    echo "<ol>";
    echo "<li>تأكد من أن قاعدة البيانات تعمل بشكل صحيح</li>";
    echo "<li>تحقق من صلاحيات المستخدم في قاعدة البيانات</li>";
    echo "<li>جرب حذف المنتجات المكررة يدوياً من قاعدة البيانات</li>";
    echo "</ol>";
}
?>
