<?php

/**
 * مكتبة بسيطة لقراءة ملفات Excel
 * تدعم ملفات .xlsx و .csv
 */
class SimpleExcelReader {
    
    private $file_path;
    private $file_type;
    private $original_filename;

    public function __construct($file_path, $original_filename = null) {
        $this->file_path = $file_path;
        $this->original_filename = $original_filename;

        try {
            $this->file_type = $this->detectFileType($file_path, $original_filename);
        } catch (Exception $e) {
            // تسجيل تفاصيل إضافية للتشخيص
            $debug_info = [
                'file_path' => $file_path,
                'original_filename' => $original_filename,
                'file_exists' => file_exists($file_path),
                'file_size' => file_exists($file_path) ? filesize($file_path) : 0,
                'pathinfo' => pathinfo($original_filename ?: $file_path)
            ];

            error_log("SimpleExcelReader Debug Info: " . json_encode($debug_info));
            throw $e;
        }
    }
    
    /**
     * تحديد نوع الملف
     */
    private function detectFileType($file_path, $original_filename = null) {
        // أولاً: محاولة التحقق من الاسم الأصلي للملف
        if ($original_filename) {
            // تنظيف اسم الملف من الأحرف الخاصة
            $clean_filename = preg_replace('/[^\w\-_\.]/', '', $original_filename);
            $extension = strtolower(pathinfo($clean_filename, PATHINFO_EXTENSION));

            // إذا لم نحصل على امتداد، نحاول الاسم الأصلي
            if (empty($extension)) {
                $extension = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
            }

            if (in_array($extension, ['csv', 'xlsx', 'xls'])) {
                return $extension === 'csv' ? 'csv' : 'excel';
            }
        }

        // ثانياً: التحقق من امتداد الملف المؤقت
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        if ($extension === 'csv') {
            return 'csv';
        } elseif (in_array($extension, ['xlsx', 'xls'])) {
            return 'excel';
        }

        // ثالثاً: محاولة التحقق من محتوى الملف
        if (file_exists($file_path)) {
            $file_content = file_get_contents($file_path, false, null, 0, 1024); // قراءة أول 1KB

            // التحقق من ملفات Excel
            if (strpos($file_content, 'PK') === 0) { // XLSX signature
                return 'excel';
            }

            if (strpos($file_content, '<?xml') !== false && strpos($file_content, 'Workbook') !== false) { // Excel XML
                return 'excel';
            }

            // التحقق من ملف CSV (يحتوي على فواصل)
            if (strpos($file_content, ',') !== false || strpos($file_content, ';') !== false) {
                return 'csv';
            }
        }

        // إذا لم نتمكن من تحديد النوع، نفترض أنه CSV
        return 'csv';
    }
    
    /**
     * قراءة الملف وإرجاع البيانات كمصفوفة
     */
    public function readFile() {
        try {
            if ($this->file_type === 'csv') {
                return $this->readCSV();
            } elseif ($this->file_type === 'excel') {
                return $this->readExcel();
            }

            // محاولة أخيرة: قراءة كـ CSV
            return $this->readCSV();
        } catch (Exception $e) {
            // إذا فشلت القراءة، نحاول الطريقة البديلة
            if ($this->file_type === 'excel') {
                try {
                    return $this->readCSV(); // محاولة قراءة Excel كـ CSV
                } catch (Exception $e2) {
                    throw new Exception('فشل في قراءة الملف. تأكد من أن الملف بصيغة Excel أو CSV صحيحة. الخطأ: ' . $e->getMessage());
                }
            } else {
                throw new Exception('فشل في قراءة الملف: ' . $e->getMessage());
            }
        }
    }
    
    /**
     * قراءة ملف CSV
     */
    private function readCSV() {
        $rows = [];
        
        // محاولة قراءة الملف مع معالجة الترميز
        try {
            $content = file_get_contents($this->file_path);
            if ($content === false) {
                throw new Exception('فشل في قراءة محتوى الملف');
            }

            // التحقق من وجود BOM وإزالته إذا لزم الأمر
            if (substr($content, 0, 3) === "\xEF\xBB\xBF") {
                $content = substr($content, 3);
            }

            // محاولة تحويل الترميز إذا لم يكن UTF-8
            if (!mb_check_encoding($content, 'UTF-8')) {
                // قائمة الترميزات الشائعة للتجربة
                $encodings_to_try = ['ISO-8859-1', 'Windows-1252', 'ASCII'];

                foreach ($encodings_to_try as $encoding) {
                    if (mb_check_encoding($content, $encoding)) {
                        $converted = mb_convert_encoding($content, 'UTF-8', $encoding);
                        if ($converted !== false) {
                            $content = $converted;
                            break;
                        }
                    }
                }
            }

            // كتابة المحتوى المحول إلى ملف مؤقت
            $temp_file = tempnam(sys_get_temp_dir(), 'csv_reader_');
            file_put_contents($temp_file, $content);

            // قراءة CSV من الملف المؤقت
            if (($handle = fopen($temp_file, 'r')) !== FALSE) {
                while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                    $rows[] = $data;
                }
                fclose($handle);
                unlink($temp_file); // حذف الملف المؤقت
            } else {
                throw new Exception('فشل في فتح الملف المؤقت');
            }

        } catch (Exception $e) {
            throw new Exception('فشل في قراءة ملف CSV: ' . $e->getMessage());
        }
        
        return $rows;
    }
    
    /**
     * قراءة ملف Excel (تحويل إلى CSV أولاً)
     */
    private function readExcel() {
        // محاولة استخدام مكتبة PhpSpreadsheet إذا كانت متوفرة
        if (class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
            return $this->readExcelWithPhpSpreadsheet();
        }
        
        // إذا لم تكن متوفرة، نحاول تحويل Excel إلى CSV
        return $this->convertExcelToCSV();
    }
    
    /**
     * قراءة Excel باستخدام PhpSpreadsheet
     */
    private function readExcelWithPhpSpreadsheet() {
        try {
            if (!class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
                throw new Exception('مكتبة PhpSpreadsheet غير متوفرة');
            }

            // محاولة تحميل الملف مع معالجة الأخطاء
            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($this->file_path);
            $worksheet = $spreadsheet->getActiveSheet();

            // الحصول على البيانات مع معالجة الأخطاء
            $data = [];
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

            for ($row = 1; $row <= $highestRow; $row++) {
                $rowData = [];
                $hasData = false;

                for ($col = 1; $col <= $highestColumnIndex; $col++) {
                    $cell = $worksheet->getCellByColumnAndRow($col, $row);
                    $value = $cell->getCalculatedValue();

                    // تحويل القيم إلى نص
                    if ($value !== null) {
                        $value = (string)$value;
                        if (!empty(trim($value))) {
                            $hasData = true;
                        }
                    } else {
                        $value = '';
                    }

                    $rowData[] = $value;
                }

                // إضافة الصف فقط إذا كان يحتوي على بيانات
                if ($hasData) {
                    $data[] = $rowData;
                }
            }

            return $data;

        } catch (Exception $e) {
            // إذا فشلت قراءة Excel، نحاول قراءته كـ CSV
            throw new Exception('فشل في قراءة ملف Excel: ' . $e->getMessage());
        }
    }
    
    /**
     * تحويل Excel إلى CSV (حل بديل)
     */
    private function convertExcelToCSV() {
        // محاولة قراءة ملف Excel كـ CSV إذا كان محفوظاً بهذا التنسيق
        if ($this->file_type === 'excel') {
            // إذا كان الملف Excel حقيقي، نحتاج مكتبة خاصة
            throw new Exception('
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>مكتبة Excel غير متوفرة</h6>
                    <p>لقراءة ملفات Excel، يرجى اتباع إحدى الطرق التالية:</p>
                    <ol>
                        <li><strong>الطريقة الأولى (الأسهل):</strong>
                            <ul>
                                <li>فتح ملف Excel في Microsoft Excel أو Google Sheets</li>
                                <li>اختيار "حفظ باسم" أو "Save As"</li>
                                <li>اختيار تنسيق "CSV (UTF-8)" أو "CSV (Comma delimited)"</li>
                                <li>رفع ملف CSV الجديد</li>
                            </ul>
                        </li>
                        <li><strong>الطريقة الثانية:</strong>
                            <ul>
                                <li>تحميل النموذج المتوفر من الموقع</li>
                                <li>نسخ بياناتك إلى النموذج</li>
                                <li>حفظ النموذج ورفعه</li>
                            </ul>
                        </li>
                    </ol>
                    <p class="mb-0"><strong>ملاحظة:</strong> النموذج المتوفر يدعم كلا التنسيقين ويعمل بشكل مثالي.</p>
                </div>
            ');
        }

        return $this->readCSV();
    }
    
    /**
     * التحقق من صحة الملف
     */
    public function validateFile() {
        if (!file_exists($this->file_path)) {
            throw new Exception('الملف غير موجود');
        }
        
        if (!is_readable($this->file_path)) {
            throw new Exception('لا يمكن قراءة الملف');
        }
        
        $file_size = filesize($this->file_path);
        if ($file_size > 5 * 1024 * 1024) { // 5MB
            throw new Exception('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        }
        
        if ($file_size === 0) {
            throw new Exception('الملف فارغ');
        }
        
        return true;
    }
    
    /**
     * الحصول على نوع الملف
     */
    public function getFileType() {
        return $this->file_type;
    }

    /**
     * الحصول على معلومات تشخيصية عن الملف
     */
    public function getDebugInfo() {
        $info = [
            'file_path' => $this->file_path,
            'original_filename' => $this->original_filename,
            'detected_type' => $this->file_type,
            'file_exists' => file_exists($this->file_path),
            'file_size' => file_exists($this->file_path) ? filesize($this->file_path) : 0,
            'is_readable' => is_readable($this->file_path)
        ];

        if ($this->original_filename) {
            $info['original_extension'] = strtolower(pathinfo($this->original_filename, PATHINFO_EXTENSION));
        }

        if (file_exists($this->file_path)) {
            $content_sample = file_get_contents($this->file_path, false, null, 0, 50);
            $info['content_sample'] = bin2hex($content_sample);
            $info['content_preview'] = substr($content_sample, 0, 20);
        }

        return $info;
    }
}

/**
 * مكتبة بسيطة لإنشاء ملفات Excel
 */
class SimpleExcelWriter {
    
    private $data = [];
    private $filename;
    
    public function __construct($filename = 'export.xlsx') {
        $this->filename = $filename;
    }
    
    /**
     * إضافة صف من البيانات
     */
    public function addRow($row_data) {
        $this->data[] = $row_data;
    }
    
    /**
     * إضافة عدة صفوف
     */
    public function addRows($rows_data) {
        foreach ($rows_data as $row) {
            $this->addRow($row);
        }
    }
    
    /**
     * تصدير البيانات كملف Excel (CSV مع تنسيق Excel)
     */
    public function export() {
        // إعداد headers للتحميل
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $this->filename . '"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');
        
        // إضافة BOM للدعم الصحيح للعربية في Excel
        echo "\xEF\xBB\xBF";
        
        // إنشاء ملف CSV (يمكن فتحه في Excel)
        $output = fopen('php://output', 'w');
        
        foreach ($this->data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
    }
    
    /**
     * حفظ الملف في مسار محدد
     */
    public function save($file_path) {
        $output = fopen($file_path, 'w');
        
        // إضافة BOM للدعم الصحيح للعربية
        fwrite($output, "\xEF\xBB\xBF");
        
        foreach ($this->data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
    }
}
?>
